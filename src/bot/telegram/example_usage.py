#!/usr/bin/env python3
"""
Example usage of the interactive Telegram menu system.
This shows how to integrate the interactive menu with your main application.
"""

from src.bot.telegram.telegram import TelegramManager
from src.bot.telegram.interactive_menu import InteractiveMenu
from src.logger import get_logger

LOGGER = get_logger(__name__)

def example_usage():
    """
    Example of how to use the interactive menu system.
    """
    # Example base configuration (this would come from your config.toml)
    base_config = {
        "general": {
            "years_ago": 20,
            "investment_amount": 1000,
            "investment_kind": "monthly",
            "animation_duration_seconds": 20,
            "fps": 30,
            "tax_rate": 0.26375,
            "tax_free_return_threshold_per_annu": 1000,
            "save_data": False
        },
        "strategies": {
            "AAPL_default": {
                "ticker": "AAPL",
                "name": "Apple",
                "reinvest_dividends": False,
                "timing": "regular"
            }
        }
    }
    
    # Initialize Telegram manager (you would pass real config here)
    telegram_config = {
        "token": "your_bot_token",
        "chat_id": "your_chat_id",
        "enabled": True,
        "interactive_menu": True,
        "interactive_timeout_minutes": 5
    }
    
    # In real usage, you would initialize this with actual Telegram credentials
    # telegram_manager = TelegramManager(telegram_config)
    
    # For this example, we'll just show the structure
    print("🎯 Interactive Menu Usage Example")
    print("="*50)
    print()
    print("1. Initialize TelegramManager with your config")
    print("2. Create InteractiveMenu instance")
    print("3. Call show_menu_and_wait() with base config")
    print("4. Handle the returned configuration")
    print()
    print("The menu will provide these options:")
    print("  1. 🔧 Use Default Config")
    print("  2. 📊 Use Predefined Strategies") 
    print("  3. 🎨 Specify Strategies Directly")
    print()
    print("Option 2 will show predefined strategies from TOML files:")
    print("  • ₿ Cryptocurrency Showdown")
    print("  • ⚡ Crypto vs Traditional Stocks")
    print("  • 💰 Dividend Strategy Focus")
    print("  • 📊 Market Indices Battle")
    print("  • 🏢 Tech Giants Showdown")
    print("  • ⏰ Timing Strategy Test")
    print()
    print("Option 3 will prompt for custom strategies with fields:")
    print("  • Ticker Symbol (normalized to uppercase)")
    print("  • Name (with smart defaults)")
    print("  • Reinvest Dividends (y/n, default: n)")
    print("  • Timing (regular/peaks/lows, default: regular)")
    print("  • Add Another Strategy? (y/n, default: n)")
    print()
    print("All prompts support 'd' for default values and have timeouts.")

def integration_example():
    """
    Example of how to integrate with your main application.
    """
    code_example = '''
# In your main application:

def main():
    # Load your base configuration
    base_config = load_config_from_toml("config.toml")
    
    # Initialize Telegram if enabled
    if base_config.get("telegram", {}).get("interactive_menu", False):
        telegram_manager = TelegramManager(base_config["telegram"])
        interactive_menu = InteractiveMenu(telegram_manager)
        
        # Show menu and get user choice
        selected_config = interactive_menu.show_menu_and_wait(base_config)
        
        # Use selected config or fall back to base config
        final_config = selected_config if selected_config else base_config
    else:
        final_config = base_config
    
    # Continue with your application using final_config
    create_wealth_animation(final_config)
'''
    
    print("🔧 Integration Example")
    print("="*50)
    print(code_example)

if __name__ == "__main__":
    example_usage()
    print()
    integration_example()
