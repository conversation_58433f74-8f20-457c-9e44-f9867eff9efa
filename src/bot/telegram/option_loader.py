"""TOML option loader for interactive menu system."""

import tomllib
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass

from src.logger import get_logger

LOGGER = get_logger(__name__)


@dataclass
class MenuOption:
    """Represents a menu option with ID, display text, and associated data."""
    
    option_id: str
    display_text: str
    description: str
    option_type: str
    strategy_data: Dict[str, Any]


class OptionLoader:
    """Loads menu options from TOML files."""
    
    def __init__(self, options_dir: str = None):
        """
        Initialize the option loader.
        
        Args:
            options_dir (str): Directory containing option TOML files.
        """
        LOGGER.activate()
        
        if options_dir is None:
            # Default to the options directory relative to this file
            self.options_dir = Path(__file__).parent / "options"
        else:
            self.options_dir = Path(options_dir)
        
        LOGGER.info(f"🔧 Option loader initialized with directory: {self.options_dir}")
    
    def load_all_options(self) -> List[MenuOption]:
        """
        Load all menu options from TOML files in the options directory.
        Files starting with underscore (_) are ignored.

        Returns:
            List[MenuOption]: List of loaded menu options.
        """
        options = []

        if not self.options_dir.exists():
            LOGGER.error(f"❌ Options directory not found: {self.options_dir}")
            return options

        # Get all TOML files in the options directory, excluding those starting with underscore
        toml_files = [f for f in self.options_dir.glob("*.toml") if not f.name.startswith('_')]

        if not toml_files:
            LOGGER.warning(f"⚠️ No enabled TOML files found in options directory: {self.options_dir}")
            return options

        # Sort files for consistent ordering
        toml_files.sort()

        for toml_file in toml_files:
            try:
                option = self._load_option_from_file(toml_file)
                if option:
                    options.append(option)
                    LOGGER.debug(f"✅ Loaded option: {option.option_id}")
            except Exception as e:
                LOGGER.error(f"❌ Failed to load option from {toml_file}: {e}")

        LOGGER.info(f"📋 Loaded {len(options)} menu options")
        return options

    def load_predefined_options(self) -> List[MenuOption]:
        """
        Load only predefined strategy options (excluding default, interactive, and disabled options).

        Returns:
            List[MenuOption]: List of predefined strategy options.
        """
        all_options = self.load_all_options()

        # Filter out default and interactive options
        predefined_options = [
            option for option in all_options
            if option.option_type == "strategies_only" and option.option_id != "default"
        ]

        LOGGER.info(f"📋 Loaded {len(predefined_options)} predefined strategy options")
        return predefined_options
    
    def _load_option_from_file(self, toml_file: Path) -> MenuOption:
        """
        Load a single menu option from a TOML file.
        
        Args:
            toml_file (Path): Path to the TOML file.
            
        Returns:
            MenuOption: Loaded menu option.
        """
        with open(toml_file, "rb") as f:
            data = tomllib.load(f)
        
        # Extract option metadata
        option_data = data.get("option", {})
        option_id = option_data.get("id", toml_file.stem)
        display_text = option_data.get("display_text", f"Option {option_id}")
        description = option_data.get("description", "No description available")
        option_type = option_data.get("type", "strategies_only")
        
        # Extract strategy data based on option type
        strategy_data = self._extract_strategy_data(data, option_type)
        
        return MenuOption(
            option_id=option_id,
            display_text=display_text,
            description=description,
            option_type=option_type,
            strategy_data=strategy_data
        )
    
    def _extract_strategy_data(self, data: Dict[str, Any], option_type: str) -> Dict[str, Any]:
        """
        Extract strategy data from TOML data based on option type.
        
        Args:
            data (Dict[str, Any]): TOML data.
            option_type (str): Type of option.
            
        Returns:
            Dict[str, Any]: Extracted strategy data.
        """
        if option_type == "use_config_file":
            return data.get("config", {"use_config_file": True})
        
        elif option_type == "strategies_only":
            return {
                "strategies": data.get("strategies", {}),
                "type": option_type
            }
        
        elif option_type == "full_config":
            # For full config replacement
            config_data = dict(data)
            # Remove the option metadata
            config_data.pop("option", None)
            return config_data
        
        elif option_type in ["interactive_full", "interactive_strategies"]:
            return {
                "type": option_type,
                "interactive": True
            }
        
        else:
            LOGGER.warning(f"⚠️ Unknown option type: {option_type}")
            return {"strategies": data.get("strategies", {})}
    
    def get_option_by_id(self, option_id: str) -> MenuOption:
        """
        Get a specific option by its ID.
        
        Args:
            option_id (str): Option ID to find.
            
        Returns:
            MenuOption: Found option or None.
        """
        options = self.load_all_options()
        for option in options:
            if option.option_id == option_id:
                return option
        return None
