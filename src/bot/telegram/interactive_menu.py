"""Interactive Telegram menu system for ReelStonks strategy selection."""

import time
from typing import Optional, Dict, Any

from src.logger import get_logger
from src.bot.telegram.telegram import TelegramManager
from src.bot.telegram.option_loader import OptionLoader
from src.bot.telegram.builders import <PERSON><PERSON>uilder, ConfigBuilder

LOGGER = get_logger(__name__)


class InteractiveMenu:
    """
    Interactive Telegram menu system with 3-level hierarchy:
    1. Main menu (Default, Predefined, Custom, Quick Crypto)
    2. Predefined submenu (if selected)
    3. Custom submenu (if selected)
    """

    def __init__(self, telegram_manager: TelegramManager):
        """
        Initialize the interactive menu system.

        Args:
            telegram_manager (TelegramManager): Telegram manager instance for sending messages.
            timeout_minutes (int): Minutes to wait for user response before using default.
        """
        LOGGER.activate()
        self.telegram_manager = telegram_manager
        self.timeout_minutes = telegram_manager.interactive_timeout_minutes
        self.timeout_seconds = self.timeout_minutes * 60
        # self.option_loader = OptionLoader()
        # self.strategy_builder = StrategyBuilder(telegram_manager, timeout_seconds=300)
        # self.config_builder = ConfigBuilder(telegram_manager, timeout_seconds=300)
        
        LOGGER.info(f"🎛️ Interactive menu initialized with {self.timeout_minutes} minute timeout")

    def show_menu_and_wait(self, base_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Show the main interactive menu and handle user selection.

        Args:
            base_config (Dict[str, Any]): Base configuration for fallback.

        Returns:
            Optional[Dict[str, Any]]: Selected configuration or None for default.
        """
        LOGGER.info("🎛️ Starting interactive menu system")
        
        # Show main menu
        main_choice = self._show_main_menu()
        
        if main_choice == 1:
            # Use default config
            LOGGER.info("🔧 User selected default configuration")
            return None
        
        elif main_choice == 2:
            # Use predefined strategies
            LOGGER.info("📋 User selected predefined strategies")
            return self._handle_predefined_strategies()
        
        elif main_choice == 3:
            # Specify config on the fly
            LOGGER.info("🎨 User selected custom configuration")
            return self._handle_custom_configuration(base_config)
        
        elif main_choice == 4:
            # Bonus option: Quick crypto comparison
            LOGGER.info("₿ User selected quick crypto comparison")
            return self._handle_quick_crypto()
        
        else:
            # Timeout or invalid choice - use default
            LOGGER.info("⏰ No valid selection, using default configuration")
            return None

    def _show_main_menu(self) -> int:
        """Show the main menu and get user selection."""
        self.telegram_manager.send_message(
            "🎯 **ReelStonks Interactive Menu**\n\n"
            f"⏱️ You have **{self.timeout_minutes} minutes** to choose an option.\n"
            "If no selection is made, the default configuration will be used.\n\n"
            "📋 **Main Options:**\n\n"
            "**1. 🔧 Use Default Config**\n"
            "   Use your current config.toml settings\n\n"
            "**2. 📊 Use Predefined Strategies**\n"
            "   Choose from ready-made strategy comparisons\n\n"
            "**3. 🎨 Specify Config on the Fly**\n"
            "   Build custom configuration interactively\n\n"
            "**4. ₿ Quick Crypto Comparison**\n"
            "   Fast Bitcoin vs Ethereum vs S&P 500 comparison\n\n"
            "💬 **Reply with the number (1-4) of your choice**\n"
            "💡 **Reply 'd' to use default (option 1)**"
        )
        
        return self._wait_for_main_menu_response()

    def _wait_for_main_menu_response(self) -> int:
        """Wait for main menu response."""
        start_time = time.time()
        
        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()
            
            for update in updates:
                if "message" in update and "text" in update["message"]:
                    text = update["message"]["text"].strip().lower()
                    
                    if text == 'd' or text == '1':
                        self.telegram_manager.send_message("✅ Using default configuration")
                        return 1
                    elif text == '2':
                        self.telegram_manager.send_message("✅ Selected predefined strategies")
                        return 2
                    elif text == '3':
                        self.telegram_manager.send_message("✅ Selected custom configuration")
                        return 3
                    elif text == '4':
                        self.telegram_manager.send_message("✅ Selected quick crypto comparison")
                        return 4
                    else:
                        self.telegram_manager.send_message(
                            f"❌ Invalid selection '{text}'. Please choose 1-4 or 'd' for default."
                        )
            
            time.sleep(1)
        
        # Timeout
        self.telegram_manager.send_message(
            f"⏰ No response received within {self.timeout_minutes} minutes. Using default configuration."
        )
        return 1

    def _handle_predefined_strategies(self) -> Optional[Dict[str, Any]]:
        """Handle predefined strategies submenu."""
        predefined_options = self.option_loader.load_predefined_options()
        
        if not predefined_options:
            self.telegram_manager.send_message("❌ No predefined strategies available. Using default configuration.")
            return None
        
        # Show predefined options
        message_lines = [
            "📊 **Predefined Strategy Options**\n",
            f"⏱️ You have **{self.timeout_minutes} minutes** to choose.\n",
            "📋 **Available Strategies:**\n"
        ]
        
        for i, option in enumerate(predefined_options, 1):
            message_lines.append(f"**{i}. {option.display_text}**")
            message_lines.append(f"   {option.description}\n")
        
        message_lines.extend([
            "💬 **Reply with the number of your choice**",
            "💡 **Reply 'd' to use default config**"
        ])
        
        self.telegram_manager.send_message("\n".join(message_lines))
        
        # Wait for selection
        selected_index = self._wait_for_predefined_response(len(predefined_options))
        
        if selected_index == 0:  # Default or timeout
            return None
        
        selected_option = predefined_options[selected_index - 1]
        self.telegram_manager.send_message(
            f"✅ **Selected: {selected_option.display_text}**\n\n"
            f"{selected_option.description}\n\n"
            "🚀 Starting animation creation..."
        )
        
        return selected_option.strategy_data

    def _wait_for_predefined_response(self, max_options: int) -> int:
        """Wait for predefined menu response."""
        start_time = time.time()
        
        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()
            
            for update in updates:
                if "message" in update and "text" in update["message"]:
                    text = update["message"]["text"].strip().lower()
                    
                    if text == 'd':
                        self.telegram_manager.send_message("Using default configuration")
                        return 0
                    
                    try:
                        choice = int(text)
                        if 1 <= choice <= max_options:
                            return choice
                        else:
                            self.telegram_manager.send_message(
                                f"❌ Invalid choice. Please choose 1-{max_options} or 'd' for default."
                            )
                    except ValueError:
                        self.telegram_manager.send_message(
                            f"❌ Invalid input '{text}'. Please choose 1-{max_options} or 'd' for default."
                        )
            
            time.sleep(1)
        
        # Timeout
        self.telegram_manager.send_message(
            f"⏰ No response received within {self.timeout_minutes} minutes. Using default configuration."
        )
        return 0

    def _handle_custom_configuration(self, base_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle custom configuration building."""
        return self.config_builder.build_config_interactive(base_config)

    def _handle_quick_crypto(self) -> Dict[str, Any]:
        """Handle quick crypto comparison option."""
        self.telegram_manager.send_message(
            "₿ **Quick Crypto Comparison**\n\n"
            "Comparing Bitcoin, Ethereum, and S&P 500 over the last 5 years!\n\n"
            "🚀 Starting animation creation..."
        )
        
        return {
            "strategies": {
                "BTC_regular": {
                    "ticker": "BTC-USD",
                    "name": "Bitcoin",
                    "reinvest_dividends": False,
                    "timing": "regular"
                },
                "ETH_regular": {
                    "ticker": "ETH-USD",
                    "name": "Ethereum",
                    "reinvest_dividends": False,
                    "timing": "regular"
                },
                "SP500_regular": {
                    "ticker": "^GSPC",
                    "name": "S&P 500",
                    "reinvest_dividends": False,
                    "timing": "regular"
                }
            },
            "general": {
                "years_ago": 5,
                "investment_amount": 1000,
                "investment_kind": "monthly",
                "animation_duration_seconds": 15
            }
        }
