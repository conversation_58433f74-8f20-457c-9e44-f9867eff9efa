"""Interactive Telegram menu system for ReelStonks strategy selection."""

import time
from typing import Optional, Dict, Any

from src.logger import get_logger
from src.bot.telegram.telegram import TelegramManager
from src.bot.telegram._build import StrategyBuilder, ConfigBuilder, OptionLoader

LOGGER = get_logger(__name__)


class InteractiveMenu:
    """
    Interactive Telegram menu system with 3-level hierarchy:
    1. Main menu (Default, Predefined, Custom, Quick Crypto)
    2. Predefined submenu (if selected)
    3. Custom submenu (if selected)
    """

    def __init__(self, telegram_manager: TelegramManager):
        """
        Initialize the interactive menu system.

        Args:
            telegram_manager (TelegramManager): Telegram manager instance for sending messages.
        """
        LOGGER.activate()
        self.telegram_manager = telegram_manager
        self.timeout_minutes = telegram_manager.interactive_timeout_minutes
        self.timeout_seconds = self.timeout_minutes * 60
        self.option_loader = OptionLoader()
        self.strategy_builder = StrategyBuilder(telegram_manager, timeout_seconds=300)
        self.config_builder = ConfigBuilder(telegram_manager, timeout_seconds=300)

        LOGGER.info(f"🎛️ Interactive menu initialized with {self.timeout_minutes} minute timeout")

    def _clear_old_messages(self):
        """Clear any old messages from the Telegram chat to avoid processing stale responses."""
        LOGGER.info("🧹 Clearing old messages...")

        # Get all pending updates and mark them as processed
        updates = self.telegram_manager._get_updates()
        if updates:
            LOGGER.info(f"🧹 Cleared {len(updates)} old messages")
        else:
            LOGGER.info("🧹 No old messages to clear")

    def show_menu_and_wait(self, base_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Show the main interactive menu and handle user selection.

        Args:
            base_config (Dict[str, Any]): Base configuration for fallback.

        Returns:
            Optional[Dict[str, Any]]: Selected configuration or None for default.
        """
        LOGGER.info("🎛️ Starting interactive menu system")

        # Clear any old messages before starting the menu
        self._clear_old_messages()

        # Show main menu
        main_choice = self._show_main_menu()

        if main_choice == 1:
            # Use default config
            LOGGER.info("🔧 User selected default configuration")
            return None

        elif main_choice == 2:
            # Use predefined strategies
            LOGGER.info("📋 User selected predefined strategies")
            return self._handle_predefined_strategies()

        elif main_choice == 3:
            # Specify strategies directly in chat
            LOGGER.info("🎨 User selected custom strategy building")
            return self._handle_custom_strategies(base_config)

        else:
            # Timeout or invalid choice - use default
            LOGGER.info("⏰ No valid selection, using default configuration")
            return None

    def _show_main_menu(self) -> int:
        """Show the main menu and get user selection."""
        self.telegram_manager.send_message(
            "🎯 **ReelStonks Interactive Menu**\n\n"
            f"⏱️ You have **{self.timeout_minutes} minutes** to choose an option.\n"
            "If no selection is made, the default configuration will be used.\n\n"
            "📋 **Main Options:**\n\n"
            "**1. 🔧 Use Default Config**\n"
            "   Use your current config.toml settings\n\n"
            "**2. 📊 Use Predefined Strategies**\n"
            "   Choose from ready-made strategy comparisons\n\n"
            "**3. 🎨 Specify Strategies Directly**\n"
            "   Build custom strategies interactively in chat\n\n"
            "💬 **Reply with the number (1-3) of your choice**\n"
            "💡 **Reply 'd' to use default (option 1)**"
        )

        # Small delay to ensure message is sent before polling starts
        time.sleep(2)

        return self._wait_for_main_menu_response()

    def _wait_for_main_menu_response(self) -> int:
        """Wait for main menu response."""
        start_time = time.time()
        menu_start_timestamp = int(start_time)  # Unix timestamp when menu was shown

        LOGGER.info(f"🕐 Waiting for user response (timeout: {self.timeout_minutes} minutes)")

        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()

            for update in updates:
                if "message" in update and "text" in update["message"]:
                    # Only process messages sent after the menu was shown
                    message_timestamp = update["message"].get("date", 0)
                    if message_timestamp < menu_start_timestamp:
                        LOGGER.info(f"🕐 Ignoring old message from {message_timestamp}")
                        continue

                    text = update["message"]["text"].strip().lower()
                    LOGGER.info(f"📱 Received user input: '{text}'")

                    if text == 'd' or text == '1':
                        self.telegram_manager.send_message("✅ Using default configuration")
                        return 1
                    elif text == '2':
                        self.telegram_manager.send_message("✅ Selected predefined strategies")
                        return 2
                    elif text == '3':
                        self.telegram_manager.send_message("✅ Selected custom strategy building")
                        return 3
                    else:
                        self.telegram_manager.send_message(
                            f"❌ Invalid selection '{text}'. Please choose 1-3 or 'd' for default."
                        )

            time.sleep(1)

        # Timeout
        self.telegram_manager.send_message(
            f"⏰ No response received within {self.timeout_minutes} minutes. Using default configuration."
        )
        return 1

    def _handle_predefined_strategies(self) -> Optional[Dict[str, Any]]:
        """Handle predefined strategies submenu."""
        predefined_options = self.option_loader.load_predefined_options()
        
        if not predefined_options:
            self.telegram_manager.send_message("❌ No predefined strategies available. Using default configuration.")
            return None
        
        # Show predefined options
        message_lines = [
            "📊 **Predefined Strategy Options**\n",
            f"⏱️ You have **{self.timeout_minutes} minutes** to choose.\n",
            "📋 **Available Strategies:**\n"
        ]
        
        for i, option in enumerate(predefined_options, 1):
            message_lines.append(f"**{i}. {option.display_text}**")
            message_lines.append(f"   {option.description}\n")
        
        message_lines.extend([
            "💬 **Reply with the number of your choice**",
            "💡 **Reply 'd' to use default config**"
        ])
        
        self.telegram_manager.send_message("\n".join(message_lines))
        
        # Wait for selection
        selected_index = self._wait_for_predefined_response(len(predefined_options))
        
        if selected_index == 0:  # Default or timeout
            return None
        
        selected_option = predefined_options[selected_index - 1]
        self.telegram_manager.send_message(
            f"✅ **Selected: {selected_option.display_text}**\n\n"
            f"{selected_option.description}\n\n"
            "🚀 Starting animation creation..."
        )
        
        return selected_option.strategy_data

    def _wait_for_predefined_response(self, max_options: int) -> int:
        """Wait for predefined menu response."""
        start_time = time.time()
        menu_start_timestamp = int(start_time)

        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()

            for update in updates:
                if "message" in update and "text" in update["message"]:
                    # Only process messages sent after the menu was shown
                    message_timestamp = update["message"].get("date", 0)
                    if message_timestamp < menu_start_timestamp:
                        continue

                    text = update["message"]["text"].strip().lower()

                    if text == 'd':
                        self.telegram_manager.send_message("Using default configuration")
                        return 0

                    try:
                        choice = int(text)
                        if 1 <= choice <= max_options:
                            return choice
                        else:
                            self.telegram_manager.send_message(
                                f"❌ Invalid choice. Please choose 1-{max_options} or 'd' for default."
                            )
                    except ValueError:
                        self.telegram_manager.send_message(
                            f"❌ Invalid input '{text}'. Please choose 1-{max_options} or 'd' for default."
                        )

            time.sleep(1)

        # Timeout
        self.telegram_manager.send_message(
            f"⏰ No response received within {self.timeout_minutes} minutes. Using default configuration."
        )
        return 0

    def _handle_custom_strategies(self, base_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle custom strategy building directly in chat."""
        LOGGER.info("🏗️ Starting custom strategy building")

        # Build strategies using the strategy builder
        strategies = self.strategy_builder.build_strategies_interactive()

        if not strategies:
            LOGGER.warning("⚠️ No strategies created, using default configuration")
            self.telegram_manager.send_message(
                "⚠️ No strategies were created. Using default configuration."
            )
            return None

        # Create result config with custom strategies but keep general settings from base
        result_config = dict(base_config)
        result_config["strategies"] = strategies

        self.telegram_manager.send_message(
            f"✅ **Custom Configuration Created!**\n\n"
            f"Created {len(strategies)} strategies:\n" +
            "\n".join([f"• {strategy['name']} ({strategy['ticker']})"
                      for strategy in strategies.values()]) +
            "\n\n🚀 Starting animation creation..."
        )

        return result_config
