"""TOML option loader for interactive menu system."""

import tomllib
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass

from src.logger import get_logger

LOGGER = get_logger(__name__)


@dataclass
class MenuOption:
    """Represents a menu option with ID, display text, and associated data."""
    
    option_id: str
    display_text: str
    description: str
    option_type: str
    strategy_data: Dict[str, Any]


class OptionLoader:
    """Loads menu options from TOML files."""
    
    def __init__(self, options_dir: str = None):
        """
        Initialize the option loader.
        
        Args:
            options_dir (str): Directory containing option TOML files.
        """
        LOGGER.activate()
        
        if options_dir is None:
            # Default to the options directory in the same _build directory
            self.options_dir = Path(__file__).parent / "options"
        else:
            self.options_dir = Path(options_dir)
        
        LOGGER.info(f"🔧 Option loader initialized with directory: {self.options_dir}")
    
    def load_all_options(self) -> List[MenuOption]:
        """
        Load all menu options from TOML files in the options directory.
        Files starting with underscore (_) are ignored.

        Returns:
            List[MenuOption]: List of loaded menu options.
        """
        options = []

        if not self.options_dir.exists():
            LOGGER.error(f"❌ Options directory not found: {self.options_dir}")
            return options

        # Get all TOML files in the options directory, excluding those starting with underscore
        toml_files = [f for f in self.options_dir.glob("*.toml") if not f.name.startswith('_')]

        if not toml_files:
            LOGGER.warning(f"⚠️ No enabled TOML files found in options directory: {self.options_dir}")
            return options

        # Sort files for consistent ordering
        toml_files.sort()

        for toml_file in toml_files:
            try:
                option = self._load_option_from_file(toml_file)
                if option:
                    options.append(option)
                    LOGGER.debug(f"✅ Loaded option: {option.option_id}")
            except Exception as e:
                LOGGER.error(f"❌ Failed to load option from {toml_file}: {e}")

        LOGGER.info(f"📋 Loaded {len(options)} menu options")
        return options

    def load_predefined_options(self) -> List[MenuOption]:
        """
        Load all predefined strategy options from TOML files.
        All files in the options directory are treated as predefined strategies.

        Returns:
            List[MenuOption]: List of predefined strategy options.
        """
        all_options = self.load_all_options()

        LOGGER.info(f"📋 Loaded {len(all_options)} predefined strategy options")
        return all_options
    
    def _load_option_from_file(self, toml_file: Path) -> MenuOption:
        """
        Load a single menu option from a TOML file.
        All TOML files in options directory are treated as predefined strategies.

        Args:
            toml_file (Path): Path to the TOML file.

        Returns:
            MenuOption: Loaded menu option.
        """
        with open(toml_file, "rb") as f:
            data = tomllib.load(f)

        # Extract option metadata
        option_data = data.get("option", {})
        option_id = option_data.get("id", toml_file.stem)
        display_text = option_data.get("display_text", f"Option {option_id}")
        description = option_data.get("description", "No description available")

        # Extract strategy data - all options are predefined strategies
        strategy_data = {
            "strategies": data.get("strategies", {})
        }

        return MenuOption(
            option_id=option_id,
            display_text=display_text,
            description=description,
            option_type="strategies_only",  # All options are predefined strategies
            strategy_data=strategy_data
        )

    def get_option_by_id(self, option_id: str) -> MenuOption:
        """
        Get a specific option by its ID.
        
        Args:
            option_id (str): Option ID to find.
            
        Returns:
            MenuOption: Found option or None.
        """
        options = self.load_all_options()
        for option in options:
            if option.option_id == option_id:
                return option
        return None
